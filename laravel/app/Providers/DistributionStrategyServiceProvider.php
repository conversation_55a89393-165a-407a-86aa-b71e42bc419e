<?php

namespace App\Providers;

use App\Services\Structure\Repositories\LineDivisionRepository;
use App\Services\Structure\Repositories\LineDivisionRepositoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\{HierarchicalChainDistributionAlgorithm,
    HierarchicalDistributionService,
    SimpleDistributionAlgorithm,
    SplitDistributionAlgorithm};
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\{
    TransactionManagerInterface,
    SettingsProviderInterface,
    LimitCalculatorInterface,
    SaleRepositoryInterface
};
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\{
    TransactionManager,
    SalesSettingsProvider,
    LimitCalculator,
    SaleDetailFactory,
    SaleCreator,
    SaleRepository
};
use App\Services\Sales\Ceiling\Strategies\Distribution\{
    DistributionStrategyFactory,
    DistributionType,
    PrivatePharmacyStrategy,
    StoreStrategy,
    LocalChainStrategy
};
use App\Services\SalesService;
use App\Services\Enums\SaleDistribution;
use Illuminate\Support\ServiceProvider;

/**
 * Service Provider for Distribution Strategy dependencies
 *
 * This provider registers all the services and dependencies needed
 * for the refactored distribution strategy system.
 */
class DistributionStrategyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Register interfaces to implementations
        $this->app->bind(TransactionManagerInterface::class, TransactionManager::class);
        $this->app->bind(SettingsProviderInterface::class, SalesSettingsProvider::class);
        $this->app->bind(LimitCalculatorInterface::class, LimitCalculator::class);
        $this->app->bind(SaleRepositoryInterface::class, SaleRepository::class);

        // Register service classes as singletons
        $this->app->singleton(SaleDetailFactory::class);
        $this->app->singleton(SaleCreator::class);
        $this->app->singleton(DistributionStrategyFactory::class);

        // FIXED: Register SalesService instances as factories (not singletons)
        // This ensures fresh instances are created for each request in Octane
        $this->app->bind('SalesService.Normal', function ($app) {
            return SalesService::make(SaleDistribution::NORMAL);
        });

        $this->app->bind('SalesService.Direct', function ($app) {
            return SalesService::make(SaleDistribution::DIRECT);
        });

        // ALTERNATIVE: Register as factory methods that always return fresh instances
        $this->app->bind('SalesService.Normal.Factory', function ($app) {
            return function() {
                return SalesService::make(SaleDistribution::NORMAL);
            };
        });

        $this->app->bind('SalesService.Direct.Factory', function ($app) {
            return function() {
                return SalesService::make(SaleDistribution::DIRECT);
            };
        });

        // Register distribution algorithms with fresh service instances
        $this->app->bind(SimpleDistributionAlgorithm::class, function ($app) {
            return new SimpleDistributionAlgorithm(
                SalesService::make(SaleDistribution::NORMAL,DistributionType::PRIVATE_PHARMACY), // Fresh instance
                $app->make(SaleDetailFactory::class)
            );
        });

        $this->app->bind(SplitDistributionAlgorithm::class, function ($app) {
            return new SplitDistributionAlgorithm(
                SalesService::make(SaleDistribution::NORMAL,DistributionType::STORES), // Fresh instance
                $app->make(SaleDetailFactory::class),
                0.9, // Primary percentage
                0.1  // Secondary percentage
            );
        });

        $this->app->bind(LineDivisionRepositoryInterface::class, LineDivisionRepository::class);

        $this->app->bind(HierarchicalChainDistributionAlgorithm::class, function ($app) {
            return new HierarchicalChainDistributionAlgorithm(
                SalesService::make(SaleDistribution::NORMAL,DistributionType::LOCAL_CHAINS), // Fresh instance
                $app->make(SaleDetailFactory::class),
                $app->make(HierarchicalDistributionService::class)
            );
        });

        // Register strategy classes
        $this->registerStrategies();
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Any bootstrapping logic can go here
    }

    /**
     * Register individual strategy classes
     *
     * @return void
     */
    private function registerStrategies(): void
    {
        $this->app->bind(PrivatePharmacyStrategy::class, function ($app) {
            return new PrivatePharmacyStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                SalesService::make(SaleDistribution::NORMAL,DistributionType::PRIVATE_PHARMACY), // Fresh instance
                new SimpleDistributionAlgorithm(
                    SalesService::make(SaleDistribution::NORMAL,DistributionType::PRIVATE_PHARMACY), // Fresh instance
                    $app->make(SaleDetailFactory::class)
                )
            );
        });

        $this->app->bind(StoreStrategy::class, function ($app) {
            return new StoreStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                SalesService::make(SaleDistribution::NORMAL,DistributionType::STORES), // Fresh instance
                new SplitDistributionAlgorithm(
                    SalesService::make(SaleDistribution::NORMAL,DistributionType::STORES), // Fresh instance
                    $app->make(SaleDetailFactory::class),
                    0.9,
                    0.1
                )
            );
        });

        $this->app->bind(LocalChainStrategy::class, function ($app) {
            return new LocalChainStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                SalesService::make(SaleDistribution::NORMAL,DistributionType::LOCAL_CHAINS), // Fresh instance
                new HierarchicalChainDistributionAlgorithm(
                    SalesService::make(SaleDistribution::NORMAL,DistributionType::LOCAL_CHAINS), // Fresh instance
                    $app->make(SaleDetailFactory::class),
                    $app->make(HierarchicalDistributionService::class)
                )
            );
        });
    }
}

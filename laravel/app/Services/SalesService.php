<?php

namespace App\Services;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SalesService
{

    private array $selections;
    private array $groupBy;
    /**
     * @var true
     */
    private bool $isNegativeCeiling = false;


    public function addSelect($select): static
    {
        $this->selections[] = $select;

        return $this;
    }

    public function forNegativeCeiling()
    {
        $this->isNegativeCeiling = true;
        return $this;
    }

    private function __construct(
        private readonly SaleDistribution $saleDistribution,
        private readonly ?DistributionType $distributionType = null
    )
    {
        $this->selections = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
            DB::raw('CAST(SUM(crm_sales_details.quantity) AS DECIMAL(64,20)) /
                   SUM(SUM(crm_sales_details.quantity)) OVER (PARTITION BY crm_sales.product_id) AS percentage')
        ];

        $this->groupBy = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
        ];

    }

    public static function make(SaleDistribution $saleDistribution, ?DistributionType $distributionType = null): static
    {
        return new static($saleDistribution, $distributionType);
    }

    private function generateCacheKey(string $functionName, array $parameters): string
    {
        $paramsString = serialize($parameters);
        $key = $functionName . '|' . $paramsString;
        return md5($key);
    }

    public function getRatiosForDistribution(string $date, int $product, array $distributorIds, array $divisionIds = []): Collection
    {
        $date = Carbon::parse($date);
        DB::statement("set sql_mode=''");

        return Cache::remember(
            $this->generateCacheKey(__FUNCTION__, [$this->saleDistribution, $this->isNegativeCeiling, ...$this->selections, ...func_get_args()]),
            now()->addHours(2),
            function () use ($date, $distributorIds, $product, $divisionIds) {
                $query = Sale::query();
                $query->select($this->selections);
                $this->buildJoins($query,$date, $product, $divisionIds);
                $ceilings = $this->getCeiling();
                $query
                    ->whereYear("sales.date", (string)$date->year)
                    ->whereMonth("sales.date", (string)$date->month)
                    ->whereIn("sales.distributor_id", $distributorIds)
                    ->whereIn("sales.ceiling", $ceilings);
                $query->groupBy($this->groupBy);
                return $query->get();
            }
        );
    }

    private function buildJoins($query,Carbon $date, int $product, array $divisionIds): void
    {
        $query
            ->join("sales_details", "sales.id", "sales_details.sale_id")
            ->join("line_products", function ($join) use ($date, $product) {
                $join
                    ->on("sales.product_id", "=", "line_products.product_id")
                    ->where("line_products.product_id", $product)
                    ->whereColumn("line_products.line_id", "sales_details.line_id")
                    ->where("line_products.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_products.to_date", ">", $date)
                            ->orWhereNull("line_products.to_date")
                    );
            })
            ->join("line_divisions", function ($join) use ($date, $divisionIds) {
                $join
                    ->on("sales_details.div_id", "=", "line_divisions.id")
                    ->whereColumn("line_divisions.line_id", "line_products.line_id")
                    ->where("line_divisions.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_divisions.to_date", ">", $date)
                            ->orWhereNull("line_divisions.to_date")
                    );

                if (!empty($divisionIds)) {
                    $join->whereIn('line_divisions.id', $divisionIds);
                }
            });

        // Add mappings joins when DistributionType is specified
        if ($this->distributionType !== null) {
            $query
                ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
                ->join("mappings", function ($join) {
                    $join->on('mapping_sale.mapping_id', 'mappings.id');

                    if ($this->distributionType === DistributionType::PRIVATE_PHARMACY) {
                        $join->where(function ($q) {
                            $q->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::STORES->value)
                                ->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::LOCAL_CHAINS->value)
                                ->orWhere('mappings.unified_pharmacy_type_id', null);
                        });
                    } else {
                        $join->where('mappings.unified_pharmacy_type_id', $this->distributionType->value);
                    }
                })
                ->where("mappings.exception", false);
        }

        if ($this->saleDistribution == SaleDistribution::NORMAL) {
            // Use LEFT JOIN for STORES distribution type, INNER JOIN for others (matching DistributionService logic)
            $joinType = ($this->distributionType === DistributionType::STORES) ? 'leftJoin' : 'join';

            $query->$joinType("product_ceilings", function ($join) use ($date) {
                $join
                    ->on("product_ceilings.product_id", "=", "sales.product_id")
                    ->where("product_ceilings.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("product_ceilings.to_date", ">", $date)
                            ->orWhereNull("product_ceilings.to_date")
                    );

                // Only add quantity constraints for INNER JOIN (non-STORES types)
                if ($this->distributionType !== DistributionType::STORES) {
                    $join->where(function ($q) {
                        $q->whereColumn('sales.quantity', '>=', 'product_ceilings.negative_units')
                            ->whereColumn('sales.quantity', '<=', 'product_ceilings.units');
                    });
                }
            });
        }
    }


    private function getCeiling(): array
    {
        return match ($this->saleDistribution) {
            SaleDistribution::NORMAL => [Ceiling::BELOW],
            SaleDistribution::DIRECT => [Ceiling::BELOW, Ceiling::DISTRIBUTED]
        };
    }
}
